extends Button

class_name lvl_up_button

@onready var lvlup_button =  $"."



signal 升级失败




func _ready():

	lvlup_button.pressed.connect(桌椅升级)
	

func 桌椅升级():
	print("=== 升级按钮被点击 ===")
	
	# 从桌椅管理器获取当前选中桌子的ID和升级价格
	var 桌子ID = 桌椅管理器.当前选中桌子ID
	print("当前选中桌子ID: ", 桌子ID)
	
	# 如果没有选中桌子，返回
	if 桌子ID == -1:
		print("错误：未选中任何桌子")
		升级失败.emit()
		return
	
	# 检查桌子是否存在于桌子列表中
	if not 桌椅管理器.桌子列表.has(桌子ID):
		print("错误：桌子ID ", 桌子ID, " 不存在于桌椅管理器的桌子列表中")
		升级失败.emit()
		return
		
	# 获取升级价格
	var 升级价格 = 桌椅管理器.桌子列表[桌子ID].升级价格
	print("桌子升级价格: ", 升级价格)
	print("当前金钱数量: ", 经济系统.当前货币数量)
	
	# 检查是否有足够的资金
	if 经济系统.当前货币数量 >= 升级价格:
		print("金钱足够，开始升级流程")
		# 减少金钱
		经济系统.当前货币数量 -= 升级价格
		print("金钱扣除完成，剩余金钱: ", 经济系统.当前货币数量)
		
		lvlup_button.disabled = true
		print("升级按钮已禁用")
		
		# 使用桌椅管理器升级桌子
		var 升级结果 = 桌椅管理器.升级桌子(桌子ID)
		print("桌椅管理器升级结果: ", 升级结果)
		
		lvlup_button.disabled = false
		print("升级按钮已重新启用")
	else:
		print("金钱不足，升级失败")
		lvlup_button.disabled = true
		升级失败.emit()
		await get_tree().create_timer(2.5).timeout
		lvlup_button.disabled = false
		print("升级按钮重新启用")
		
		
