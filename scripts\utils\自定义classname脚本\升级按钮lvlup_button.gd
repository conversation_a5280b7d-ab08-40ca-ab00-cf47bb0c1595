extends Button

class_name lvl_up_button

@onready var lvlup_button =  $"."



signal 升级失败




func _ready():

	lvlup_button.pressed.connect(桌椅升级)
	

func 桌椅升级():
	# 从桌椅管理器获取当前选中桌子的ID和升级价格
	var 桌子ID = 桌椅管理器.当前选中桌子ID
	
	# 如果没有选中桌子，返回
	if 桌子ID == -1:
		升级失败.emit()
		return
	
	# 检查桌子是否存在于桌子列表中
	if not 桌椅管理器.桌子列表.has(桌子ID):
		升级失败.emit()
		return
		
	# 获取升级价格
	var 升级价格 = 桌椅管理器.桌子列表[桌子ID].升级价格
	
	# 检查是否有足够的资金
	if 经济系统.当前货币数量 >= 升级价格:
		# 减少金钱
		经济系统.当前货币数量 -= 升级价格
		lvlup_button.disabled = true
		
		# 使用桌椅管理器升级桌子
		桌椅管理器.升级桌子(桌子ID)
		
		lvlup_button.disabled = false
	else:
		lvlup_button.disabled = true
		升级失败.emit()
		await get_tree().create_timer(2.5).timeout
		lvlup_button.disabled = false
		
