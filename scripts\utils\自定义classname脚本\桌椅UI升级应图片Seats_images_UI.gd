extends Sprite2D  # 继承Sprite2D类，用于显示UI中的桌椅图片

class_name Seats_images_UI  # 定义类名，方便其他脚本引用

var 当前桌子等级  # 存储当前桌子的等级，用于决定显示哪个等级的图片
@onready var 当前桌号 = self.get_parent().get_parent().get_name()  # 获取当前桌子的编号，从父节点的父节点的名称中获取

func _process(delta):
	# 从新系统中获取当前选中桌子的类型并转换为等级
	当前桌子等级 = 获取当前选中桌子等级()  # 使用辅助函数获取当前桌子等级
	
	# 根据桌子等级和当前Sprite的名称决定是否显示
	if 当前桌子等级 == 0:  # 如果是0级桌子（未购买或找不到）
		if self.name == "1":  # 显示1级图片（普通桌子的预览）
			self.visible = true
		else:
			self.visible = false
	elif 当前桌子等级 == 1:  # 如果是1级桌子（普通桌子）
		if self.name == "2":  # 显示2级图片（高级桌子的预览）
			self.visible = true
		else:
			self.visible = false
	elif 当前桌子等级 >= 2:  # 如果是2级或更高级别的桌子（高级或豪华）
		if self.name == "3":  # 显示3级图片（豪华桌子的预览）
			self.visible = true
		else:
			self.visible = false

# 辅助函数：获取当前选中桌子的等级
func 获取当前选中桌子等级() -> int:
	# 检查是否有选中的桌子
	if 桌椅管理器.当前选中桌子ID == -1:
		return 0
	
	# 检查选中的桌子是否存在于桌子列表中
	if not 桌椅管理器.当前选中桌子ID in 桌椅管理器.桌子列表:
		return 0
	
	# 获取选中桌子的类型
	var 桌子类型 = 桌椅管理器.桌子列表[桌椅管理器.当前选中桌子ID].类型
	
	# 将桌椅管理器的类型枚举转换为显示等级数值
	match 桌子类型:
		桌椅管理器.桌椅类型.空:	# 空类型对应显示等级0
			return 0
		桌椅管理器.桌椅类型.普通:	# 普通类型对应显示等级1
			return 1
		桌椅管理器.桌椅类型.高级:	# 高级类型对应显示等级2
			return 2
		桌椅管理器.桌椅类型.豪华:	# 豪华类型对应显示等级3
			return 3
	
	return 0	# 默认返回0表示未知类型或错误状态
