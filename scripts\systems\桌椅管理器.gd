extends Node  # 继承Node类以便作为autoload使用

# 桌椅类型
enum 桌椅类型 {空,普通, 高级, 豪华}  # 定义桌子的三种等级

# 旧系统引用
var 全局桌子变量  # 存储对旧系统的引用，用于数据同步

# 兼容旧系统的变量
var 当前桌等级
var 当前桌升级价格 
var 升级菜单Node 
var 当前菜单Node 
var 新增桌号: int = 0  # 对应旧系统的新增桌号
var 是第几个大堂: int = 1  # 对应旧系统的是第几个大堂
var 当前大堂位置 = "2"  # 对应旧系统的当前大堂位置
var 当前大堂数量 = 0  # 对应旧系统的当前大堂数量
var 当前所在大堂名称 = "大堂"  # 对应旧系统的当前所在大堂名称
@export var 大堂数量 = {}  # 对应旧系统的大堂数量

# 桌子数据结构
class 桌子数据:
	var 桌号
	var ID : int  # 桌子唯一ID
	var 类型 : int  # 桌子类型，对应桌椅类型枚举
	var 容量 : int  # 可容纳客人数
	var 质量 : float  # 桌子质量，影响客人满意度
	var 升级价格 : float  # 升级到下一级的价格
	var 节点路径 : String  # 场景中桌子节点的路径
	var 是否占用 : bool  # 是否有客人正在使用
	
	# 构造函数，初始化桌子信息
	func _init(id,号,类, 容, 质, 价, 路径):
		ID = id  # 设置ID
		桌号 = 号 # 设置桌号
		类型 = 类  # 设置类型
		容量 = 容  # 设置容量
		质量 = 质  # 设置质量
		升级价格 = 价  # 设置升级价格
		节点路径 = 路径  # 设置节点路径
		是否占用 = false  # 初始为未占用

# 桌椅列表
var 桌子列表 : Dictionary = {}  # 存储所有桌子，格式 {桌子ID: 桌子对象}
var 最大桌子ID : int = 0  # 当前使用的最大ID，用于生成新ID

# 当前选中的桌子ID，对应旧系统的当前桌号
var 当前选中桌子ID : int = -1  # 初始化为-1表示未选中任何桌子

# 信号
signal 桌子状态变化(桌子ID, 是否占用)  # 桌子占用状态变化时发出
signal 桌子升级(桌子ID, 新类型)  # 桌子升级时发出
signal 添加新桌子(桌子ID)  # 添加新桌子时发出

func _ready():
	# 获取旧系统引用，但不进行数据导入，保持桌椅集合为空状态
	if Engine.has_singleton("Quanju_table_var"):
		# 获取旧系统的引用，用于兼容性但不导入数据
		全局桌子变量 = Engine.get_singleton("Quanju_table_var")
		
		# 注释掉旧系统数据导入逻辑，确保游戏启动时桌椅集合为空
		# for 桌号 in 全局桌子变量.桌子等级:
		#	var 等级 = 全局桌子变量.桌子等级[桌号]
		#	var 类型 = 转换等级到类型(等级)
		#	全局桌子变量.等级对应升级价格(等级)
		#	var 价格 = 全局桌子变量.升级价格
		#	注册桌子(桌号, 类型, 4, 价格)
		
		# 注释掉当前选中桌子的设置，保持未选中状态
		# if 全局桌子变量.当前桌号 in 全局桌子变量.桌子等级:
		#	设置当前桌子(全局桌子变量.当前桌号)
		
		# 连接信号以监听旧系统变化，保持兼容性
		全局桌子变量.connect("property_changed", self._on_old_system_changed)
	
	# 创建定时器用于定期同步旧系统数据，保持系统间的数据一致性
	var 同步定时器 = Timer.new()
	# 设置定时器每秒触发一次，用于检查和同步数据变化
	同步定时器.wait_time = 1.0
	# 连接定时器超时信号到同步函数，实现定期数据同步
	同步定时器.timeout.connect(self._同步旧系统)
	# 将定时器添加为桌椅管理器的子节点，使其能够正常工作
	add_child(同步定时器)
	# 启动定时器开始定期同步工作
	同步定时器.start()
	
	# 删除自动初始化1-6号桌子的代码，确保游戏启动时桌椅集合为空
	# 现在桌子只有在玩家点击桌椅升级按钮时才会被注册到系统中
	print("桌椅管理器初始化完成，当前桌椅集合为空，等待玩家点击注册")

# 将旧系统的等级转换为新系统的类型
func 转换等级到类型(等级):
	if 等级 <= 0:  # 未购买或最低级
		return 桌椅类型.普通  # 返回普通类型
	elif 等级 <= 3:  # 中等级别
		return 桌椅类型.高级  # 返回高级类型
	else:  # 高等级别
		return 桌椅类型.豪华  # 返回豪华类型

# 将新系统的类型转换为旧系统的等级
func 转换类型到等级(类型):
	match 类型:  # 根据类型返回对应的等级
		桌椅类型.空 :
			return 0  # 空类型对应等级0
		桌椅类型.普通:
			return 1  # 普通类型对应等级1
		桌椅类型.高级:
			return 3  # 高级类型对应等级3
		桌椅类型.豪华:
			return 5  # 豪华类型对应等级5
	return 0  # 默认返回0表示未知类型

# 监听旧系统变化
func _on_old_system_changed(属性名, 新值):
	if 属性名 == "当前桌号":  # 如果旧系统的当前桌号发生变化
		设置当前桌子(新值)  # 同步到新系统
	elif 属性名 == "桌子等级":  # 如果旧系统的桌子等级发生变化
		_同步旧系统()  # 执行完整同步

# 定期同步旧系统数据
func _同步旧系统():
	if not 全局桌子变量:  # 检查旧系统引用是否存在
		return  # 如果不存在则直接返回
		
	# 同步当前桌号
	if 全局桌子变量.当前桌号 != 获取当前桌号():  # 检查当前桌号是否一致
		设置当前桌子(全局桌子变量.当前桌号)  # 如果不一致则同步
	
	# 同步桌子等级
	for 桌号 in 全局桌子变量.桌子等级:  # 遍历旧系统中的所有桌子
		var 旧等级 = 全局桌子变量.桌子等级[桌号]  # 获取旧系统中桌子的等级
		
		# 查找对应的新系统桌子
		var 找到的ID = -1  # 初始化为-1表示未找到
		for ID in 桌子列表:  # 遍历新系统中的所有桌子
			if 桌子列表[ID].桌号 == 桌号:  # 检查桌号是否匹配
				找到的ID = ID  # 如果匹配则记录ID
				break  # 找到后退出循环
		
		if 找到的ID != -1:  # 如果找到了对应的桌子
			var 新类型 = 转换等级到类型(旧等级)  # 将旧系统的等级转换为新系统的类型
			if 桌子列表[找到的ID].类型 != 新类型:  # 检查类型是否一致
				桌子列表[找到的ID].类型 = 新类型  # 如果不一致则更新类型
				
				# 更新质量和升级价格
				match 新类型:  # 根据新类型设置属性
					桌椅类型.普通:
						桌子列表[找到的ID].质量 = 1.0  # 设置普通桌子质量
						全局桌子变量.等级对应升级价格(旧等级)  # 计算升级价格
						桌子列表[找到的ID].升级价格 = 全局桌子变量.升级价格  # 更新升级价格
					桌椅类型.高级:
						桌子列表[找到的ID].质量 = 1.5  # 设置高级桌子质量
						全局桌子变量.等级对应升级价格(旧等级)  # 计算升级价格
						桌子列表[找到的ID].升级价格 = 全局桌子变量.升级价格  # 更新升级价格
					桌椅类型.豪华:
						桌子列表[找到的ID].质量 = 2.0  # 设置豪华桌子质量
						全局桌子变量.等级对应升级价格(旧等级)  # 计算升级价格
						桌子列表[找到的ID].升级价格 = 全局桌子变量.升级价格  # 更新升级价格
				
				桌子升级.emit(找到的ID, 新类型)  # 发出桌子升级信号
		else:  # 如果没有找到对应的桌子
			# 创建新桌子
			var 新类型 = 转换等级到类型(旧等级)  # 将旧系统的等级转换为新系统的类型
			全局桌子变量.等级对应升级价格(旧等级)  # 计算升级价格
			var 价格 = 全局桌子变量.升级价格  # 获取计算结果
			注册桌子(桌号, 新类型, 4, 价格)  # 创建新桌子，使用同步的数据

# 获取当前选中桌子的桌号
func 获取当前桌号():
	if 当前选中桌子ID != -1 and 当前选中桌子ID in 桌子列表:  # 检查当前选中桌子是否有效
		return 桌子列表[当前选中桌子ID].桌号  # 返回当前选中桌子的桌号
	return "12138"   # 默认返回"0"

# 设置当前桌子
func 设置当前桌子(桌号):
	# 初始化查找结果为-1，表示未找到匹配的桌子
	var 找到的ID = -1
	# 遍历桌椅管理器中所有已注册的桌子
	for ID in 桌子列表:
		# 将传入的桌号转换为字符串格式进行比较，确保数据类型一致
		if 桌子列表[ID].桌号 == str(桌号):
			找到的ID = ID
			break
	
	# 如果在桌子列表中找到了匹配的桌子
	if 找到的ID != -1:
		# 更新桌椅管理器的当前选中桌子ID，用于后续的桌椅操作和UI显示
		当前选中桌子ID = 找到的ID
		print("成功设置当前桌子ID为：", 找到的ID, "，桌号为：", 桌号)
	else:
		# 如果没有找到匹配的桌子，设置为未选中状态
		当前选中桌子ID = -1
		print("未找到桌号为", 桌号, "的桌子")
	
	# 调用函数更新当前桌子的等级信息，用于UI显示和游戏逻辑
	更新当前桌等级()

# 注册桌子
func 注册桌子(桌号, 类型=桌椅类型.普通, 容量=4, 价格=null):
	最大桌子ID += 1  # 递增ID，确保每个桌子有唯一ID
	var 质量 = 1.0  # 初始质量值，基础桌子的质量基准
	var 升级价格 = 价格  # 使用传入的价格参数，如果有的话
	
	# 如果没有传入价格，根据类型设置默认价格
	if 升级价格 == null:
		match 类型:  # 根据桌子类型设置不同的属性值
			桌椅类型.空:
				质量 = 0.0  # 普通桌子基础质量
				升级价格 = 500  # 普通桌子的升级价格
			桌椅类型.普通:
				质量 = 1.0  # 普通桌子基础质量
				升级价格 = 1000.0  # 普通桌子的升级价格
			桌椅类型.高级:
				质量 = 1.5  # 高级桌子质量提升50%
				升级价格 = 2000.0  # 高级桌子的升级价格
			桌椅类型.豪华:
				质量 = 2.0  # 豪华桌子质量是普通桌子的两倍
				升级价格 = 0.0  # 已是最高级，无需升级价格
	
	var 桌子节点路径 = "桌子/" + 桌号  # 生成桌子在场景树中的节点路径
	var 新桌子 = 桌子数据.new(最大桌子ID, 桌号, 类型, 容量, 质量, 升级价格, 桌子节点路径)  # 创建桌子对象，包含所有必要信息
	桌子列表[最大桌子ID] = 新桌子  # 添加到桌子列表字典中
	添加新桌子.emit(最大桌子ID)  # 发出添加新桌子的信号，通知其他系统
	
	# 同步到旧系统
	if 全局桌子变量 and not 桌号 in 全局桌子变量.桌子等级:  # 检查旧系统中是否已存在此桌子
		var 等级 = 转换类型到等级(类型)  # 将新系统的类型转换为旧系统的等级
		全局桌子变量.桌子等级[桌号] = 等级  # 在旧系统中添加此桌子
	
	return 最大桌子ID  # 返回新桌子的ID，方便后续引用
	# 调试输出：验证桌子注册信息
	print("已注册桌子 - ID:%d, 桌号:%s, 类型:%s, 容量:%d, 质量:%f, 升级价格:%f" % [最大桌子ID, 桌号, 类型, 容量, 质量, 升级价格])

# 设置桌子占用状态
func 设置桌子占用(桌子ID, 占用状态):
	if 桌子列表.has(桌子ID):  # 检查桌子是否存在
		桌子列表[桌子ID].是否占用 = 占用状态  # 设置占用状态
		桌子状态变化.emit(桌子ID, 占用状态)  # 发出状态变化信号

# 升级桌子
func 升级桌子(桌子ID):
	if not 桌子列表.has(桌子ID):  # 检查桌子是否存在
		return false  # 桌子不存在
		
	var 桌子 = 桌子列表[桌子ID]  # 获取桌子对象
	
	# 已是最高级
	if 桌子.类型 == 桌椅类型.豪华:  # 检查是否已是最高级
		return false  # 无法继续升级
	
	# 检查是否有足够的钱
	if Engine.has_singleton("经济系统"):  # 检查经济系统是否存在
		if 经济系统.减少金钱(桌子.升级价格):  # 尝试扣钱
			# 升级桌子
			桌子.类型 += 1  # 提升类型等级
			
			# 更新桌子属性
			match 桌子.类型:  # 根据新类型设置新属性
				桌椅类型.高级:
					桌子.质量 = 1.5  # 更新为高级桌子质量
					桌子.升级价格 = 2000.0  # 设置下一级升级价格
				桌椅类型.豪华:
					桌子.质量 = 2.0  # 更新为豪华桌子质量
					桌子.升级价格 = 0.0  # 已是最高级，无需升级
			
			桌子升级.emit(桌子ID, 桌子.类型)  # 发出桌子升级信号
			# 同步到旧系统
			if 全局桌子变量 and 桌子.桌号 in 全局桌子变量.桌子等级:  # 检查旧系统是否有此桌子
				var 旧等级 = 全局桌子变量.桌子等级[桌子.桌号]  # 获取旧系统中桌子的等级
				var 新等级 = 转换类型到等级(桌子.类型)  # 将新系统的类型转换为旧系统的等级
				if 新等级 > 旧等级:  # 确保只能升级不能降级
					全局桌子变量.桌子等级[桌子.桌号] = 新等级  # 更新旧系统中的等级
			
			return true  # 升级成功
	
	return false  # 升级失败

# 查找空闲桌子
func 查找空闲桌子(所需容量=1):
	var 候选桌子 = []  # 存储所有满足条件的桌子ID
	
	for 桌子ID in 桌子列表:  # 遍历所有桌子
		var 桌子 = 桌子列表[桌子ID]  # 获取桌子对象
		if not 桌子.是否占用 and 桌子.容量 >= 所需容量:  # 检查是否空闲且容量足够
			候选桌子.append(桌子ID)  # 添加到候选列表
	
	# 按照桌子质量排序，优先使用质量高的桌子
	候选桌子.sort_custom(func(a, b): return 桌子列表[a].质量 > 桌子列表[b].质量)  # 按质量降序排列
	
	if 候选桌子.size() > 0:  # 检查是否有候选桌子
		return 候选桌子[0]  # 返回质量最高的桌子ID
	
	return -1  # 没有找到合适的桌子

# 更新当前桌等级变量的方法，在设置当前桌子时调用
func 更新当前桌等级():
	if 当前选中桌子ID != -1 and 当前选中桌子ID in 桌子列表:
		var 桌子类型 = 桌子列表[当前选中桌子ID].类型
		# 根据桌椅类型枚举正确设置当前桌等级
		match 桌子类型:
			桌椅类型.空:	# 空类型对应等级0
				当前桌等级 = 0
			桌椅类型.普通:	# 普通类型对应等级1
				当前桌等级 = 1
			桌椅类型.高级:	# 高级类型对应等级2
				当前桌等级 = 2
			桌椅类型.豪华:	# 豪华类型对应等级3
				当前桌等级 = 3
	else:
		当前桌等级 = 0	# 未选中任何桌子时设置为0
