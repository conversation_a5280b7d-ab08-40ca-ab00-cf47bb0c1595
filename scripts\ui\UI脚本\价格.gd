extends Label



var 当前查看的桌子等级 = 0

func _process(_delta):
	# 从新系统桌椅管理器获取当前选中桌子的升级价格和类型信息
	if 桌椅管理器.当前选中桌子ID != -1 and 桌椅管理器.当前选中桌子ID in 桌椅管理器.桌子列表:
		# 获取当前选中的桌子对象，用于读取桌椅的详细信息
		var 当前桌子 = 桌椅管理器.桌子列表[桌椅管理器.当前选中桌子ID]
		var 升级价格 = 当前桌子.升级价格	# 获取桌椅升级到下一级所需的金钱数量
		var 桌子类型 = 当前桌子.类型	# 获取桌椅的当前类型等级
		当前查看的桌子等级 = 获取桌子显示等级(桌子类型)	# 将桌椅类型转换为显示等级数值
		
		# 检查桌椅是否已达到最高级别（豪华级别）
		if 桌子类型 == 桌椅管理器.桌椅类型.豪华:
			# 桌椅已达最高级，显示最高级提示文字而不是价格数字
			$".".text = "已达最高级"
		else:
			# 桌椅还可以继续升级，显示升级所需的金钱数量
			$".".text = str(升级价格)
	else:
		# 如果没有选中桌椅或选中的桌椅不存在于系统中，显示默认状态
		$".".text = "0"
		当前查看的桌子等级 = 0

# 将桌椅管理器的类型枚举转换为显示等级数值的辅助函数
func 获取桌子显示等级(桌子类型) -> int:
	# 根据桌椅管理器中的类型枚举，返回对应的显示等级数值
	match 桌子类型:
		桌椅管理器.桌椅类型.空:	# 空类型桌椅对应显示等级0（未购买状态）
			return 0
		桌椅管理器.桌椅类型.普通:	# 普通类型桌椅对应显示等级1
			return 1
		桌椅管理器.桌椅类型.高级:	# 高级类型桌椅对应显示等级2
			return 2
		桌椅管理器.桌椅类型.豪华:	# 豪华类型桌椅对应显示等级3（当前最高级）
			return 3
	return 0	# 未知类型默认返回等级0
