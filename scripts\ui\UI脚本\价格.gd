extends Label

var 当前查看的桌子等级 = 0

func _process(_delta):
	# 从新系统桌椅管理器获取当前选中桌子的升级价格
	if 桌椅管理器.当前选中桌子ID != -1 and 桌椅管理器.当前选中桌子ID in 桌椅管理器.桌子列表:
		# 直接从桌椅管理器获取当前选中桌子的升级价格
		var 当前桌子 = 桌椅管理器.桌子列表[桌椅管理器.当前选中桌子ID]
		var 升级价格 = 当前桌子.升级价格	# 获取桌子的当前升级价格
		当前查看的桌子等级 = 获取桌子显示等级(当前桌子.类型)	# 获取桌子的显示等级
		
		# 更新价格显示文本
		$".".text = str(升级价格)
	else:
		# 如果没有选中桌子或桌子不存在，显示默认价格
		$".".text = "0"
		当前查看的桌子等级 = 0

# 将桌椅管理器的类型枚举转换为显示等级的辅助函数
func 获取桌子显示等级(桌子类型) -> int:
	match 桌子类型:
		桌椅管理器.桌椅类型.空:	# 空类型对应显示等级0
			return 0
		桌椅管理器.桌椅类型.普通:	# 普通类型对应显示等级1
			return 1
		桌椅管理器.桌椅类型.高级:	# 高级类型对应显示等级2
			return 2
		桌椅管理器.桌椅类型.豪华:	# 豪华类型对应显示等级3
			return 3
	return 0	# 默认返回0
