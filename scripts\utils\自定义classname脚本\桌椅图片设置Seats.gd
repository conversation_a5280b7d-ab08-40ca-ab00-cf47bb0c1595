extends Sprite2D  # 继承Sprite2D类，用于显示桌椅图片
class_name Seats_images  # 定义类名，方便其他脚本引用

var 当前桌子等级  # 存储当前桌子的等级，用于决定显示哪个等级的图片
@onready var 当前桌号 = self.get_parent().get_parent().get_name()  # 获取当前桌子的编号，从父节点的父节点的名称中获取

func _process(delta: float) -> void:
	# 直接使用新系统获取桌子类型并转换为显示等级
	# 不再依赖旧系统的当前桌等级变量
	当前桌子等级 = 获取新系统桌子等级(当前桌号)  # 从新系统获取当前桌子等级
	
	# 调用桌子图片设置函数，更新桌子的显示状态
	桌子图片设置()

# 从新系统获取桌子等级的辅助函数
func 获取新系统桌子等级(桌号: String) -> int:
	# 在新系统中查找对应桌号的桌子
	for ID in 桌椅管理器.桌子列表:
		if 桌椅管理器.桌子列表[ID].桌号 == 桌号:
			# 将桌椅管理器的类型枚举转换为显示等级数值
			match 桌椅管理器.桌子列表[ID].类型:
				桌椅管理器.桌椅类型.空:	# 空类型对应显示等级0
					return 0
				桌椅管理器.桌椅类型.普通:	# 普通类型对应显示等级1
					return 1
				桌椅管理器.桌椅类型.高级:	# 高级类型对应显示等级2
					return 2
				桌椅管理器.桌椅类型.豪华:	# 豪华类型对应显示等级3
					return 3
			break
	
	# 如果在新系统中没有找到，返回0表示未购买或未找到
	return 0

func 桌子图片设置():
	# 获取当前桌子编号
	var 桌号 = self.get_parent().get_parent().get_name()  # 获取桌子编号，从父节点的父节点名称中获取
	
	# 获取当前桌子在新系统中的等级
	var 此桌等级 = 获取新系统桌子等级(桌号)  # 调用辅助函数获取桌子等级
	
	# 如果等级超过3，限制为3（最高等级）
	if 此桌等级 >= 3:  # 确保等级不超过最高等级3
		此桌等级 = 3  # 限制为最高等级3
	
	# 根据桌子等级和当前Sprite的名称决定是否显示
	if self.name == str(此桌等级):  # 如果当前Sprite的名称与桌子等级相匹配
		self.visible = true  # 显示当前等级对应的图片
	else:  # 如果当前Sprite的名称与桌子等级不匹配
		self.visible = false  # 隐藏不匹配等级的图片
