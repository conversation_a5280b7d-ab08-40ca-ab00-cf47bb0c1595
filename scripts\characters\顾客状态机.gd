extends Node  # 继承Node类作为状态机管理器
class_name 顾客状态机  # 定义类名以便其他脚本使用

# 状态枚举 - 基于旧系统分析得出的完整状态列表
enum 状态类型 {
    等待,    # 初始等待状态
    游走,    # 对应旧系统的CustomerWalk状态
    排队,    # 对应旧系统的CustomerArray状态  
    移动,    # 通用移动状态
    入座,    # 新增：拖拽到座位后的状态
    点菜,    # 顾客点菜状态
    用餐,    # 顾客用餐状态
    结账,    # 顾客结账状态
    离开     # 顾客离开状态
}

# 当前状态管理
var 当前状态 = 状态类型.等待  # 初始状态为等待
var 拥有者: 顾客基类 = null  # 状态机所属的顾客实例引用
var 状态参数 = {}  # 存储当前状态的相关参数和数据

# 导航和移动相关属性（从旧系统迁移）
var 导航代理: NavigationAgent2D  # 导航代理引用
var 目标位置: Marker2D  # 当前移动目标位置
var 移动速度: float = 300.0  # 移动速度，可以随机调整
var 距离阈值: float = 13.5  # 到达目标的距离阈值

# 排队和位置管理（从旧系统迁移）
var 排队位置: Marker2D  # 排队目标位置
var 消失位置: Marker2D  # 离开时的消失位置
var 当前座位: Node2D = null  # 当前占用的座位引用

# 状态处理函数字典，将每个状态映射到对应的处理函数
var 状态处理 = {
    状态类型.等待: func(): _处理等待状态(),  # 等待状态的处理函数
    状态类型.游走: func(): _处理游走状态(),  # 游走状态的处理函数（对应旧CustomerWalk）
    状态类型.排队: func(): _处理排队状态(),  # 排队状态的处理函数（对应旧CustomerArray）
    状态类型.移动: func(): _处理移动状态(),  # 通用移动状态的处理函数
    状态类型.入座: func(): _处理入座状态(),  # 入座状态的处理函数（新增）
    状态类型.点菜: func(): _处理点菜状态(),  # 点菜状态的处理函数
    状态类型.用餐: func(): _处理用餐状态(),  # 用餐状态的处理函数
    状态类型.结账: func(): _处理结账状态(),  # 结账状态的处理函数
    状态类型.离开: func(): _处理离开状态()   # 离开状态的处理函数
}

# 信号定义
signal 状态变化(旧状态, 新状态)  # 当状态变化时发出信号
signal 到达目标位置()  # 当顾客到达目标位置时发出信号
signal 移动开始()  # 当顾客开始移动时发出信号
signal 移动结束()  # 当顾客停止移动时发出信号

# 构造函数，初始化状态机并绑定拥有者
func _init(owner: 顾客基类):
    拥有者 = owner  # 设置状态机的拥有者（顾客实例）

# 状态机准备完成时的初始化操作
func _ready():
    # 获取拥有者的导航代理引用，用于移动控制
    if 拥有者.has_node("导航/NavigationAgent2D"):
        导航代理 = 拥有者.get_node("导航/NavigationAgent2D")

# 每帧处理当前状态的逻辑
func _process(delta):
    if 状态处理.has(当前状态):  # 检查当前状态是否有对应的处理函数
        状态处理[当前状态].call()  # 调用对应状态的处理函数

# 物理帧处理，主要用于移动相关的物理计算
func _physics_process(delta):
    # 在物理帧中处理移动逻辑，确保移动的平滑性
    if 当前状态 in [状态类型.游走, 状态类型.排队, 状态类型.移动, 状态类型.离开]:
        _执行物理移动(delta)

# 切换状态的核心函数
func 切换状态(新状态, 参数 = {}):
    var 旧状态 = 当前状态  # 保存旧状态用于信号发送
    _退出当前状态()  # 执行当前状态的退出逻辑
    当前状态 = 新状态  # 设置新状态
    状态参数 = 参数  # 更新状态参数
    _进入新状态()  # 执行新状态的进入逻辑
    状态变化.emit(旧状态, 新状态)  # 发出状态变化信号

# 状态退出时的清理逻辑
func _退出当前状态():
    match 当前状态:
        状态类型.移动, 状态类型.游走, 状态类型.排队:
            # 停止移动相关的状态时，清除移动标志
            if 拥有者:
                拥有者.is_moving = false

# 状态进入时的初始化逻辑
func _进入新状态():
    match 当前状态:
        状态类型.游走:
            # 进入游走状态时，设置随机移动速度和消失位置
            移动速度 = randf_range(200, 500)
            消失位置 = Customer_var.当前室外客人预消失目标位置
            目标位置 = 消失位置
            _设置导航目标()
        状态类型.排队:
            # 进入排队状态时，设置排队位置和移动速度
            排队位置 = Customer_var.当前室外客人排队目标位置
            消失位置 = Customer_var.当前室外客人预消失目标位置
            移动速度 = randf_range(200, 500)
            _确定排队目标位置()
        状态类型.入座:
            # 进入入座状态时，停止移动并播放待机动画
            if 拥有者:
                拥有者.is_moving = false
                if 拥有者.顾客动画播放:
                    拥有者.顾客动画播放.play("待机")

# 设置导航目标位置的通用函数
func _设置导航目标():
    if 导航代理 and 目标位置:
        导航代理.target_position = 目标位置.position  # 设置导航目标位置
        导航代理.target_position = 导航代理.get_final_position()  # 获取最终导航位置

# 确定排队状态下的目标位置
func _确定排队目标位置():
    if Quanju_var.当前店状态 == "开":  # 如果店铺开门状态
        if 排队位置:
            目标位置 = 排队位置  # 设置目标为排队位置
            _设置导航目标()
        else:
            目标位置 = 消失位置  # 没有排队位置则准备离开
            _设置导航目标()
    elif Quanju_var.当前店状态 == "关":  # 如果店铺关门状态
        目标位置 = 消失位置  # 直接设置为消失位置
        _设置导航目标()

# 执行物理移动的核心函数
func _执行物理移动(delta):
    if not 导航代理 or not 拥有者:
        return  # 如果没有导航代理或拥有者，直接返回
    
    var 距离目标距离 = 导航代理.distance_to_target()  # 获取到目标的距离
    拥有者.距离目的地位置 = 距离目标距离  # 更新拥有者的距离信息
    
    if 距离目标距离 > 距离阈值:  # 如果距离目标较远，继续移动
        var 移动方向 = 拥有者.to_local(导航代理.get_next_path_position()).normalized()  # 计算移动方向
        拥有者.velocity = 移动方向 * 移动速度  # 设置移动速度向量
        拥有者.move_and_slide()  # 执行移动
        
        # 更新移动状态和动画
        if not 拥有者.is_moving:
            拥有者.is_moving = true  # 设置为移动状态
            移动开始.emit()  # 发出移动开始信号
        
        # 播放移动动画
        if 拥有者.顾客动画播放:
            拥有者.顾客动画播放.play("移动")
    else:  # 如果已经接近目标
        # 停止移动
        拥有者.velocity = Vector2.ZERO  # 清零速度
        if 拥有者.is_moving:
            拥有者.is_moving = false  # 设置为非移动状态
            移动结束.emit()  # 发出移动结束信号
        
        # 播放待机动画
        if 拥有者.顾客动画播放:
            拥有者.顾客动画播放.play("待机")
        
        # 发出到达目标信号
        到达目标位置.emit()

# ========== 状态处理函数实现 ==========

# 处理等待状态的逻辑
func _处理等待状态():
    # 等待状态下不执行特殊逻辑，等待外部触发状态切换
    pass

# 处理游走状态的逻辑（对应旧系统CustomerWalk）
func _处理游走状态():
    # 游走状态下的移动逻辑已在_physics_process中处理
    # 检查是否到达消失位置
    if 导航代理 and 导航代理.distance_to_target() <= 距离阈值:
        # 到达消失位置，销毁顾客
        if 拥有者:
            拥有者.queue_free()

# 处理排队状态的逻辑（对应旧系统CustomerArray）
func _处理排队状态():
    # 排队状态下的移动逻辑已在_physics_process中处理
    # 检查室内排队人数限制
    if Customer_var.室内排队人数 >= 4:
        # 室内人满，不进入排队，继续等待或离开
        return
    
    # 检查是否到达排队位置
    if 导航代理 and 导航代理.distance_to_target() <= 距离阈值:
        # 到达排队位置，可以考虑进入室内或其他状态转换
        到达目标位置.emit()

# 处理通用移动状态的逻辑
func _处理移动状态():
    # 通用移动状态，移动逻辑已在_physics_process中处理
    # 检查是否到达目标
    if 导航代理 and 导航代理.distance_to_target() <= 距离阈值:
        # 根据状态参数决定下一步行为
        if 状态参数.has("下一状态"):
            切换状态(状态参数["下一状态"])
        else:
            切换状态(状态类型.等待)  # 默认切换到等待状态

# 处理入座状态的逻辑（新增功能）
func _处理入座状态():
    # 入座状态下，顾客应该保持在座位上
    # 可以在这里添加入座后的行为，比如自动点菜
    if 状态参数.has("自动点菜") and 状态参数["自动点菜"]:
        # 延迟一段时间后自动切换到点菜状态
        await 拥有者.get_tree().create_timer(2.0).timeout
        切换状态(状态类型.点菜)

# 处理点菜状态的逻辑
func _处理点菜状态():
    # 点菜状态的具体实现，可以显示菜单UI等
    # 暂时使用延迟后自动切换到用餐状态
    if not 状态参数.has("点菜开始时间"):
        状态参数["点菜开始时间"] = Time.get_ticks_msec()
    
    var 点菜时长 = (Time.get_ticks_msec() - 状态参数["点菜开始时间"]) / 1000.0
    if 点菜时长 >= 3.0:  # 点菜3秒后切换到用餐
        切换状态(状态类型.用餐)

# 处理用餐状态的逻辑
func _处理用餐状态():
    # 用餐状态的具体实现
    # 暂时使用延迟后自动切换到结账状态
    if not 状态参数.has("用餐开始时间"):
        状态参数["用餐开始时间"] = Time.get_ticks_msec()
    
    var 用餐时长 = (Time.get_ticks_msec() - 状态参数["用餐开始时间"]) / 1000.0
    if 用餐时长 >= 10.0:  # 用餐10秒后切换到结账
        切换状态(状态类型.结账)

# 处理结账状态的逻辑
func _处理结账状态():
    # 结账状态的具体实现
    # 暂时使用延迟后自动切换到离开状态
    if not 状态参数.has("结账开始时间"):
        状态参数["结账开始时间"] = Time.get_ticks_msec()
    
    var 结账时长 = (Time.get_ticks_msec() - 状态参数["结账开始时间"]) / 1000.0
    if 结账时长 >= 2.0:  # 结账2秒后切换到离开
        切换状态(状态类型.离开)

# 处理离开状态的逻辑
func _处理离开状态():
    # 设置离开目标位置
    if not 目标位置:
        目标位置 = Customer_var.当前室外客人预消失目标位置
        _设置导航目标()
    
    # 离开状态下的移动逻辑已在_physics_process中处理
    # 检查是否到达消失位置
    if 导航代理 and 导航代理.distance_to_target() <= 距离阈值:
        # 到达消失位置，销毁顾客
        if 拥有者:
            拥有者.queue_free()

# ========== 公共接口函数 ==========

# 设置顾客入座到指定座位
func 设置入座(座位节点: Node2D):
    当前座位 = 座位节点  # 记录当前座位
    状态参数["座位"] = 座位节点  # 在状态参数中保存座位信息
    状态参数["自动点菜"] = true  # 设置自动点菜标志
    切换状态(状态类型.入座)  # 切换到入座状态

# 强制顾客离开（比如店铺关门时调用）
func 强制离开():
    切换状态(状态类型.离开)  # 直接切换到离开状态

# 获取当前状态的字符串表示（用于调试）
func 获取状态名称() -> String:
    match 当前状态:
        状态类型.等待: return "等待"
        状态类型.游走: return "游走"
        状态类型.排队: return "排队"
        状态类型.移动: return "移动"
        状态类型.入座: return "入座"
        状态类型.点菜: return "点菜"
        状态类型.用餐: return "用餐"
        状态类型.结账: return "结账"
        状态类型.离开: return "离开"
        _: return "未知状态"